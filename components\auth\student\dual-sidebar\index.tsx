// Export all student dual sidebar components
export { EnhancedSidebarStudent } from './enhanced-sidebar-student.client';
export { StudentTopNav } from './student-top-nav.client';
export { LayoutWrapper } from './layout-wrapper.client';

// Re-export shared components for convenience
export { RightSidebar } from '../../shared/dual-sidebar/right-sidebar';
export { UserNav } from '../../shared/dual-sidebar/user-nav.client';

// Export the main layout wrapper as default for easy importing
export { LayoutWrapper as default } from './layout-wrapper.client';

// Legacy Sidebar component for backward compatibility
import { EnhancedSidebarStudent } from './enhanced-sidebar-student.client';
import React from 'react';

interface SidebarProps {
  children?: React.ReactNode;
}

export const Sidebar = ({ children }: SidebarProps) => {
  return (
    <EnhancedSidebarStudent>
      {children}
    </EnhancedSidebarStudent>
  );
};