// components/RouteProtectionWrapper.client.tsx
'use client';

import { useRouteProtection } from '../utils/route-protection';

interface RouteProtectionWrapperProps {
  children: React.ReactNode;
}

export const RouteProtectionWrapper: React.FC<RouteProtectionWrapperProps> = ({ children }) => {
  // This runs the route protection logic on the client side
  const { user, loading } = useRouteProtection();
  
  // Following Blade's philosophy: no loading states, instant rendering
  // The useRouteProtection hook handles redirects automatically
  return <>{children}</>;
};