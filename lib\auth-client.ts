// lib/auth-client.ts - Unified Better Auth client
import { createAuthClient } from "better-auth/react";
import { emailOTPClient, usernameClient, adminClient, organizationClient } from "better-auth/client/plugins";
import { useMemo } from 'react';
import type { 
  ExtendedSession, 
  UseUnifiedSessionReturn, 
  User<PERSON><PERSON>,
  Extended<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TeacherUser,
  SchoolAdminUser
} from './types/auth';

// Create a single unified auth client
export const authClient = createAuthClient({
  baseURL: typeof window !== 'undefined' ? window.location.origin : "http://localhost:3000",
  basePath: "/api/auth", // Single base path for all authentication
  fetchOptions: {
    credentials: "include"
  },
  plugins: [
    usernameClient(), // For students
    emailOTPClient(), // For teachers and school admins
    adminClient(), // For admin functionality
    organizationClient() // For school organizations
  ]
});

// Export all auth methods
export const {
  signIn,
  signUp,
  signOut,
  getSession,
  useSession,
  emailOtp,
  admin,
  organization
} = authClient;

/**
 * A unified session hook that handles all user roles in a single Better Auth instance.
 * It transforms the session data to match our extended user types.
 */
export const useUnifiedSession = (): UseUnifiedSessionReturn => {
  const { data: session, isPending: loading } = useSession();

  // Transform the session data to our extended types
  const transformedSession = useMemo(() => {
    if (!session?.user) {
      return { 
        session: null, 
        signOut: async () => {}, 
        role: null 
      };
    }

    const user = session.user as any;
    const role = user.role as UserRole;

    let extendedUser: ExtendedUser;

    switch (role) {
      case 'school_admin':
        extendedUser = {
          ...user,
          role: 'school_admin' as const,
          slug: user.slug || user.id,
          schoolName: user.schoolName,
          schoolAddress: user.schoolAddress,
          schoolPlaceId: user.schoolPlaceId,
          schoolType: user.schoolType,
          schoolDistrict: user.schoolDistrict,
          studentCount: user.studentCount,
          teacherCount: user.teacherCount,
        } as SchoolAdminUser;
        break;

      case 'teacher':
        extendedUser = {
          ...user,
          role: 'teacher' as const,
          slug: user.slug || user.id,
          isIndependent: user.isIndependent ?? true,
          schoolId: user.schoolId,
          department: user.department,
          subjects: user.subjects,
          isVerified: user.isVerified ?? false,
        } as TeacherUser;
        break;

      case 'student':
      default:
        extendedUser = {
          ...user,
          role: 'student' as const,
          slug: user.slug || user.id,
          teacherId: user.teacherId || '',
          isActive: user.isActive ?? false,
          classId: user.classId,
          grade: user.grade,
        } as StudentUser;
        break;
    }

    const extendedSession: ExtendedSession = {
      user: extendedUser,
      session: session.session
    };

    return {
      session: extendedSession,
      signOut,
      role
    };
  }, [session]);

  return { ...transformedSession, loading };
};

// Role-specific hooks for convenience
export const useStudentAuth = () => {
  const { session, signOut, loading } = useUnifiedSession();
  return {
    session: session?.user.role === 'student' ? session : null,
    signOut,
    loading,
    role: 'student' as const
  };
};

export const useTeacherAuth = () => {
  const { session, signOut, loading } = useUnifiedSession();
  return {
    session: session?.user.role === 'teacher' ? session : null,
    signOut,
    loading,
    role: 'teacher' as const
  };
};

export const useSchoolAuth = () => {
  const { session, signOut, loading } = useUnifiedSession();
  return {
    session: session?.user.role === 'school_admin' ? session : null,
    signOut,
    loading,
    role: 'school_admin' as const
  };
};