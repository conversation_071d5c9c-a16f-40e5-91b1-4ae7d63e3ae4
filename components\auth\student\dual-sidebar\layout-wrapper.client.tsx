'use client';
import type { ReactNode } from 'react';
import { EnhancedSidebarStudent } from './enhanced-sidebar-student.client';
import { SidebarProvider } from '../../../../stores/sidebar-store.client';

interface LayoutWrapperProps {
  children: ReactNode;
  showUserNav?: boolean;
  showTopNav?: boolean;
  className?: string;
}

/**
 * LayoutWrapper - Student version similar to TanStack Start's _root.tsx
 * 
 * This component wraps the student application with the horizontal dual sidebar layout.
 * It provides the same functionality as the TanStack Start root component
 * but adapted for Blade framework and student-specific needs.
 * 
 * Features:
 * - Horizontal top navigation with logo, hamburger menu, and icon links
 * - Hamburger menu opens flyout from left side
 * - Icon buttons use Blade Link components for direct page navigation
 * - UserNav component on the right side
 * - Full-width main content by default, adjusts when sidebars open
 * - Clean, writing-focused design for students
 * 
 * Usage:
 * - Wrap your entire student app or specific routes with this component
 * - Similar to how TanStack Start uses _root.tsx to wrap all routes
 * - Provides consistent dual sidebar layout across all student pages
 */
export function LayoutWrapper({ 
  children, 
  showUserNav = true, 
  showTopNav = true,
  className 
}: LayoutWrapperProps) {
  return (
    <SidebarProvider>
      <EnhancedSidebarStudent 
        showUserNav={showUserNav}
        showTopNav={showTopNav}
        className={className}
      >
        {children}
      </EnhancedSidebarStudent>
    </SidebarProvider>
  );
}

/**
 * Alternative export for direct use as a layout component
 * This can be used in Blade's routing system or as a wrapper component
 */
export default LayoutWrapper;