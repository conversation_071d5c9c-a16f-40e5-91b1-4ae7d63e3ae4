// pages/test-student-sidebar.tsx - Public test page for student dual sidebar
import { LayoutWrapper } from '../components/auth/student/dual-sidebar';
import { useSidebarState } from '../stores/sidebar-store.client';

export default function TestStudentSidebarPage() {
  return (
    <LayoutWrapper 
      showUserNav={true}
      showTopNav={true}
    >
      <TestStudentSidebarContent />
    </LayoutWrapper>
  );
}

function TestStudentSidebarContent() {
  const { 
    isRightSidebarOpen, 
    rightSidebarContent, 
    activeFlyout,
    isRightFlyoutOpen,
    updateRightSidebarContent,
    toggleRightSidebar 
  } = useSidebarState();

  const handleOpenUserInfo = () => {
    updateRightSidebarContent('user-info');
    if (!isRightSidebarOpen) toggleRightSidebar();
  };

  const handleOpenSettings = () => {
    updateRightSidebarContent('settings');
    if (!isRightSidebarOpen) toggleRightSidebar();
  };

  const handleOpenNotifications = () => {
    updateRightSidebarContent('notifications');
    if (!isRightSidebarOpen) toggleRightSidebar();
  };

  return (
    <div className="p-4 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-black dark:text-white">Student Dual Sidebar Test</h1>
          <p className="text-black/70 dark:text-white/70 mt-2 text-lg">
            Test the student horizontal dual sidebar functionality with clean, writing-focused design
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={handleOpenUserInfo}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            User Info
          </button>
          <button
            onClick={handleOpenSettings}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Settings
          </button>
          <button
            onClick={handleOpenNotifications}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            Notifications
          </button>
          {isRightSidebarOpen && (
            <button
              onClick={toggleRightSidebar}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              Close Sidebar
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <TestCard
          title="Horizontal Top Navigation"
          description="Clean, horizontal layout optimized for students"
          features={[
            "Logo positioned on the left",
            "Hamburger menu opens left flyout",
            "Icon buttons use Blade Link components",
            "Direct page navigation for student features",
            "UserNav component on the right",
            "Full-width design by default"
          ]}
        />
        <TestCard
          title="Student-Focused Design"
          description="Writing-focused interface for academic work"
          features={[
            "Clean, minimal design aesthetic",
            "Full-width main content area",
            "Optimized for reading and writing",
            "Student-specific navigation items",
            "Dashboard, Assignments, Courses, etc.",
            "Light/dark theme support"
          ]}
        />
        <TestCard
          title="Smart Layout System"
          description="Dynamic width adjustments based on sidebar state"
          features={[
            "Full width when sidebars closed",
            "Left flyout: ml-[284px] adjustment",
            "Right sidebar: mr-[350px] to mr-[700px]",
            "Mobile responsive with Sheet components",
            "Smooth transitions and animations",
            "Reuses existing sidebar state management"
          ]}
        />
      </div>

      <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10">
        <h2 className="text-2xl font-semibold text-black dark:text-white mb-6">Student Navigation Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-black dark:text-white mb-3">Hamburger Menu (Flyout)</h3>
            <ul className="space-y-2 text-black/70 dark:text-white/70">
              <li>• Opens flyout from left side (284px width)</li>
              <li>• Student-specific quick actions</li>
              <li>• Submit assignments, join classes</li>
              <li>• Access to library and study groups</li>
              <li>• Help center and settings</li>
              <li>• Mobile Sheet integration</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-medium text-black dark:text-white mb-3">Direct Navigation Links</h3>
            <ul className="space-y-2 text-black/70 dark:text-white/70">
              <li>• Dashboard - Student overview</li>
              <li>• Assignments - View and submit work</li>
              <li>• Courses - Access course materials</li>
              <li>• Calendar - Schedule and deadlines</li>
              <li>• Grades - Academic performance</li>
              <li>• Messages - Communication hub</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10">
        <h2 className="text-2xl font-semibold text-black dark:text-white mb-6">Current State</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <StateIndicator
            label="Right Sidebar"
            value={isRightSidebarOpen ? "Open" : "Closed"}
            color={isRightSidebarOpen ? "green" : "red"}
          />
          <StateIndicator
            label="Current Content"
            value={rightSidebarContent || "None"}
            color="blue"
          />
          <StateIndicator
            label="Left Flyout"
            value={activeFlyout || "None"}
            color="purple"
          />
          <StateIndicator
            label="Right Flyout"
            value={isRightFlyoutOpen ? "Open" : "Closed"}
            color="orange"
          />
        </div>
      </div>

      <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10">
        <h2 className="text-2xl font-semibold text-black dark:text-white mb-6">Layout Architecture</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-black dark:text-white mb-3">Component Structure</h3>
            <ul className="space-y-2 text-black/70 dark:text-white/70 font-mono text-sm">
              <li>• <span className="text-blue-600 dark:text-blue-400">StudentAuthWrapper</span> (layout.tsx)</li>
              <li>• <span className="text-green-600 dark:text-green-400">LayoutWrapper</span> (dual-sidebar wrapper)</li>
              <li>• <span className="text-purple-600 dark:text-purple-400">EnhancedSidebarStudent</span> (main layout)</li>
              <li>• <span className="text-orange-600 dark:text-orange-400">StudentTopNav</span> (horizontal nav)</li>
              <li>• <span className="text-red-600 dark:text-red-400">UserNav</span> (reused from school)</li>
              <li>• <span className="text-pink-600 dark:text-pink-400">RightSidebar</span> (reused from school)</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-medium text-black dark:text-white mb-3">Width Calculations</h3>
            <ul className="space-y-2 text-black/70 dark:text-white/70 font-mono text-sm">
              <li>• Default: <span className="text-blue-600 dark:text-blue-400">Full width</span></li>
              <li>• Left flyout: <span className="text-green-600 dark:text-green-400">ml-[284px]</span></li>
              <li>• Right sidebar: <span className="text-purple-600 dark:text-purple-400">mr-[350px]</span></li>
              <li>• Both flyouts: <span className="text-orange-600 dark:text-orange-400">mr-[700px]</span></li>
              <li>• Mobile: <span className="text-red-600 dark:text-red-400">Sheet overlays</span></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

function TestCard({ title, description, features }: {
  title: string;
  description: string;
  features: string[];
}) {
  return (
    <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-colors">
      <h3 className="text-lg font-semibold text-black dark:text-white mb-2">{title}</h3>
      <p className="text-black/60 dark:text-white/60 text-sm mb-4">{description}</p>
      <ul className="space-y-1">
        {features.map((feature, index) => (
          <li key={index} className="text-black/50 dark:text-white/50 text-xs">• {feature}</li>
        ))}
      </ul>
    </div>
  );
}

function StateIndicator({ label, value, color }: {
  label: string;
  value: string;
  color: string;
}) {
  const colorClasses = {
    green: "text-green-600 dark:text-green-400",
    red: "text-red-600 dark:text-red-400",
    blue: "text-blue-600 dark:text-blue-400",
    purple: "text-purple-600 dark:text-purple-400",
    orange: "text-orange-600 dark:text-orange-400"
  };

  return (
    <div className="flex flex-col items-center p-4 bg-white/30 dark:bg-black/20 rounded-lg border border-black/5 dark:border-white/5">
      <span className="text-black/60 dark:text-white/60 text-sm font-medium">{label}</span>
      <span className={`text-sm font-bold mt-1 ${colorClasses[color as keyof typeof colorClasses]}`}>
        {value}
      </span>
    </div>
  );
}
