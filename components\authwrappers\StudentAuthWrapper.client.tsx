
// components/authwrappers/StudentAuthWrapper.client.tsx
'use client';

import { useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useRedirect } from 'blade/hooks';
import { LayoutWrapper } from '../auth/student/dual-sidebar/layout-wrapper.client';

interface StudentAuthWrapperProps {
  children: React.ReactNode;
}

export const StudentAuthWrapper: React.FC<StudentAuthWrapperProps> = ({ children }) => {
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  const redirect = useRedirect();
  
  useEffect(() => {
    // Only redirect when we're sure about auth state
    if (loading) return;
    
    // Redirect if not authenticated
    if (!user) {
      redirect('/login?role=student');
      return;
    }
    
    // Redirect if wrong role
    if (user.role !== 'student') {
      const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
      redirect(`/${rolePrefix}/${user.slug}`);
      return;
    }
  }, [user, loading, redirect]);
  
  // Don't render anything while loading or if user is null/wrong role
  if (loading || !user || user.role !== 'student') {
    return null;
  }
  
  return (
    <LayoutWrapper 
      showUserNav={true}
      showTopNav={true}
    >
      {children}
    </LayoutWrapper>
  );
};