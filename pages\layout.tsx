// pages/layout.tsx
import { useMetadata } from 'blade/server/hooks';
import { useLocation } from 'blade/hooks';
import { Navigation } from '../components/home/<USER>';
import CanvasAnimation from '../components/home/<USER>';
import { RouteProtectionWrapper } from '../components/RouteProtectionWrapper.client';
import { Toaster } from 'sonner';
import { useEffect } from 'react';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const location = useLocation();

  useMetadata({
    title: 'Penned',
    description: 'Empowering Truth in Education - Where authentic learning meets innovative teaching',
    openGraph: {
      title: 'Penned - Empowering Truth in Education',
      description: 'Create, share, and verify educational content with confidence',
      siteName: 'Penned',
    }
  });

  // Check if we're on a protected route that has its own navigation
  const isProtectedRoute = location.pathname.startsWith('/login') ||
                            location.pathname.startsWith('/test-sidebar') || 
                          location.pathname.startsWith('/teacher') || 
                          location.pathname.startsWith('/school') || 
                          location.pathname.startsWith('/student');

  // Check if we're on an authenticated route that uses dual-sidebar (which has custom scrollbars)
  const isAuthenticatedRoute = location.pathname.startsWith('/teacher') || 
                              location.pathname.startsWith('/school') || 
                              location.pathname.startsWith('/student');

  // Manage scrollbar styling based on route type
  useEffect(() => {
    if (isAuthenticatedRoute) {
      // Hide browser scrollbars on authenticated routes (dual-sidebar has custom scrollbars)
      document.body.classList.add('hide-browser-scrollbar');
      document.documentElement.classList.add('hide-browser-scrollbar');
      document.body.classList.remove('styled-browser-scrollbar');
      document.documentElement.classList.remove('styled-browser-scrollbar');
    } else {
      // Style browser scrollbars on public routes to match custom-scrollbar design
      document.body.classList.remove('hide-browser-scrollbar');
      document.documentElement.classList.remove('hide-browser-scrollbar');
      document.body.classList.add('styled-browser-scrollbar');
      document.documentElement.classList.add('styled-browser-scrollbar');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('hide-browser-scrollbar', 'styled-browser-scrollbar');
      document.documentElement.classList.remove('hide-browser-scrollbar', 'styled-browser-scrollbar');
    };
  }, [isAuthenticatedRoute]);
  
  return (
    <div className={`min-h-screen min-w-full bg-fixed root ${isAuthenticatedRoute ? 'hide-browser-scrollbar' : ''}`}>
      <Toaster duration={6000} />
      <RouteProtectionWrapper>
        <CanvasAnimation />
        {/* Only render the main Navigation on non-protected routes */}
        {!isProtectedRoute && <Navigation />}
        <main>{children}</main>
      </RouteProtectionWrapper>
    </div>
  );
}
