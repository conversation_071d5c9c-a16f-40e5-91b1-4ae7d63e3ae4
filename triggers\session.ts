// triggers/session.ts
import type { Add<PERSON>rigger, SetTrigger, RemoveTrigger } from 'blade/types';

// Trigger for creating sessions
export const add: AddTrigger = (query) => {
  // Set default timestamps
  query.with.createdAt = new Date();
  query.with.updatedAt = new Date();
  
  // Set default expiration (24 hours from now)
  if (!query.with.expiresAt) {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    query.with.expiresAt = expiresAt;
  }
  
  return query;
};

// Trigger for updating sessions
export const set: SetTrigger = (query) => {
  // Update timestamp
  query.to.updatedAt = new Date();
  
  return query;
};

// Trigger for removing sessions (cleanup)
export const remove: RemoveTrigger = (query) => {
  // Add any cleanup logic here if needed
  return query;
};