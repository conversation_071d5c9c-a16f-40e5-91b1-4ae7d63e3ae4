// pages/school/index.tsx
import { useRightSidebarState } from '../../stores/sidebar-store.client';

export default function SchoolDashboard() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">School Dashboard</h1>
          <p className="text-gray-300 mt-2">Welcome to your school management system</p>
        </div>
        <TestRightSidebarButton />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardCard
          title="Students"
          value="1,234"
          description="Total enrolled students"
          icon="👥"
        />
        <DashboardCard
          title="Teachers"
          value="89"
          description="Active teaching staff"
          icon="👨‍🏫"
        />
        <DashboardCard
          title="Classes"
          value="45"
          description="Active classes this semester"
          icon="📚"
        />
        <DashboardCard
          title="Attendance"
          value="94.2%"
          description="Average attendance rate"
          icon="📊"
        />
        <DashboardCard
          title="Events"
          value="12"
          description="Upcoming school events"
          icon="📅"
        />
        <DashboardCard
          title="Notifications"
          value="8"
          description="Unread notifications"
          icon="🔔"
        />
      </div>

      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <ActivityItem
            title="New student enrollment"
            description="John Doe has been enrolled in Grade 10"
            time="2 hours ago"
          />
          <ActivityItem
            title="Teacher schedule updated"
            description="Math schedule for Mrs. Smith has been modified"
            time="4 hours ago"
          />
          <ActivityItem
            title="Event reminder"
            description="Parent-teacher conference scheduled for next week"
            time="1 day ago"
          />
        </div>
      </div>
    </div>
  );
}

function TestRightSidebarButton() {
  const { isOpen, toggle, setContent } = useRightSidebarState();

  const handleToggle = () => {
    if (!isOpen) {
      setContent('user-info');
    }
    toggle();
  };

  return (
    <button
      onClick={handleToggle}
      className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
    >
      {isOpen ? 'Close' : 'Open'} Right Sidebar
    </button>
  );
}

function DashboardCard({ title, value, description, icon }: {
  title: string;
  value: string;
  description: string;
  icon: string;
}) {
  return (
    <div className="bg-white/5 rounded-lg p-6 hover:bg-white/10 transition-colors">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        <span className="text-2xl">{icon}</span>
      </div>
      <div className="space-y-2">
        <p className="text-3xl font-bold text-white">{value}</p>
        <p className="text-gray-300 text-sm">{description}</p>
      </div>
    </div>
  );
}

function ActivityItem({ title, description, time }: {
  title: string;
  description: string;
  time: string;
}) {
  return (
    <div className="flex items-start space-x-3 p-3 rounded-md hover:bg-white/5 transition-colors">
      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
      <div className="flex-1">
        <h4 className="text-white font-medium">{title}</h4>
        <p className="text-gray-300 text-sm">{description}</p>
        <p className="text-gray-400 text-xs mt-1">{time}</p>
      </div>
    </div>
  );
}