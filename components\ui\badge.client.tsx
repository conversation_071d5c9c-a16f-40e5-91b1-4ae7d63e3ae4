'use client' // @NOTE: Add in case you are using Next.js

import { motion } from 'motion/react'
import { useRender } from '@base-ui-components/react/use-render'

import { cn } from '../../lib/utils'

const getVariantStyles = (variant: string): string => {
  switch (variant) {
    case 'default':
      return cn(
        'relative overflow-hidden rounded-xl border border-transparent bg-neutral-900 px-3 py-1 text-neutral-200 shadow-inner transition-all duration-200',
        'shadow-main-foreground/70 hover:bg-main-invert/90 dark:shadow-main-foreground/80 dark:hover:bg-main-foreground/56'
      )
    case 'outline':
      return cn(
        'relative overflow-hidden rounded-full border border-border bg-main-background px-3 py-1 transition-all duration-200',
        'text-primary-foreground hover:bg-main-foreground/50'
      )
    case 'success':
      return cn(
        'rounded-full bg-gradient-to-t from-green-700 to-green-600 px-3 py-1 text-white'
      )
    case 'destructive':
      return cn(
        'rounded-full bg-gradient-to-t from-red-600 to-red-500 px-3 py-1 text-white'
      )
    case 'shine':
      return cn(
        'animate-shine items-center justify-center rounded-full border border-border bg-[length:400%_100%]',
        'px-3 py-1 text-primary-invert/90 transition-colors dark:text-primary-muted',
        "bg-[linear-gradient(110deg,#000000,45%,#303030,55%,#000000)]",
        'dark:bg-[linear-gradient(110deg,#000103,45%,#303030,55%,#000103)]'
      )
    default:
      return getVariantStyles('default')
  }
}

export interface BadgeProps extends useRender.ComponentProps<'div'> {
  variant?: 'default' | 'outline' | 'success' | 'destructive' | 'shine' | 'animated-border' | 'rotate-border'
}

export function Badge({
  variant = 'default',
  render = <div />,
  className,
  children,
  ...props
}: BadgeProps) {
  // Handle special variants that need custom structure
  if (variant === 'animated-border') {
    const defaultProps = {
      className: cn(
        'relative rounded-full border border-primary/10 bg-main-background px-3 py-1 duration-200 hover:bg-main-foreground/40 font-medium text-xs',
        className
      ),
      children: (
        <>
          <div
            className={cn(
              '-inset-px pointer-events-none absolute rounded-[inherit] border border-transparent [mask-clip:padding-box,border-box]',
              '[mask-composite:intersect] [mask-image:linear-gradient(transparent,transparent),linear-gradient(#000,#000)]'
            )}
          >
            <motion.div
              className={cn(
                'absolute aspect-square bg-gradient-to-r from-transparent via-neutral-300 to-neutral-400',
                'dark:from-transparent dark:via-neutral-600 dark:to-neutral-400'
              )}
              animate={{
                offsetDistance: ['0%', '100%'],
              }}
              style={{
                width: 20,
                offsetPath: `rect(0 auto auto 0 round ${20}px)`,
              }}
              transition={{
                repeat: Number.POSITIVE_INFINITY,
                duration: 5,
                ease: 'linear',
              }}
            />
          </div>
          <span className="relative z-10 text-primary-muted">
            {children}
          </span>
        </>
      ),
      ...props
    } as Record<string, unknown>

    return useRender({
      render,
      props: defaultProps,
    })
  }

  if (variant === 'rotate-border') {
    const defaultProps = {
      className: cn('relative inline-flex overflow-hidden rounded-full p-px font-medium text-xs', className),
      children: (
        <>
          <span
            className={cn(
              'absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#4e4e4e_0%,#d4d4d4_50%,#414141_100%)]',
              'dark:bg-[conic-gradient(from_90deg_at_50%_50%,#c2c2c2_0%,#505050_50%,#bebebe_100%)]'
            )}
          />
          <span
            className={cn(
              'inline-flex size-full items-center justify-center rounded-full bg-main-background px-3 py-1 text-primary-foreground backdrop-blur-3xl'
            )}
          >
            {children}
          </span>
        </>
      ),
      ...props
    } as Record<string, unknown>

    return useRender({
      render,
      props: defaultProps,
    })
  }

  // Standard variants
  const defaultProps = {
    className: cn('font-medium text-xs', getVariantStyles(variant), className),
    children,
    ...props
  } as Record<string, unknown>

  return useRender({
    render,
    props: defaultProps,
  })
}