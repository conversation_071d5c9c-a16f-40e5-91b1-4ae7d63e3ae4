// pages/teacher/index.tsx
import { useRightSidebarState } from '../../stores/sidebar-store.client';

export default function TeacherDashboard() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Teacher Dashboard</h1>
          <p className="text-gray-300 mt-2">Manage your classes and students</p>
        </div>
        <TestRightSidebarButton />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardCard
          title="My Classes"
          value="6"
          description="Active classes this semester"
          icon="📚"
        />
        <DashboardCard
          title="Students"
          value="156"
          description="Total students across all classes"
          icon="👥"
        />
        <DashboardCard
          title="Assignments"
          value="23"
          description="Pending assignments to grade"
          icon="📝"
        />
        <DashboardCard
          title="Attendance"
          value="92.8%"
          description="Average class attendance"
          icon="📊"
        />
        <DashboardCard
          title="Messages"
          value="12"
          description="Unread messages from students"
          icon="💬"
        />
        <DashboardCard
          title="Upcoming"
          value="5"
          description="Upcoming deadlines"
          icon="⏰"
        />
      </div>

      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Today's Schedule</h2>
        <div className="space-y-3">
          <ScheduleItem
            time="09:00 AM"
            subject="Mathematics"
            class="Grade 10A"
            room="Room 201"
          />
          <ScheduleItem
            time="11:00 AM"
            subject="Mathematics"
            class="Grade 10B"
            room="Room 201"
          />
          <ScheduleItem
            time="02:00 PM"
            subject="Advanced Calculus"
            class="Grade 12"
            room="Room 205"
          />
        </div>
      </div>
    </div>
  );
}

function TestRightSidebarButton() {
  const { isOpen, toggle, setContent } = useRightSidebarState();

  const handleToggle = () => {
    if (!isOpen) {
      setContent('settings');
    }
    toggle();
  };

  return (
    <button
      onClick={handleToggle}
      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      {isOpen ? 'Close' : 'Open'} Settings
    </button>
  );
}

function DashboardCard({ title, value, description, icon }: {
  title: string;
  value: string;
  description: string;
  icon: string;
}) {
  return (
    <div className="bg-white/5 rounded-lg p-6 hover:bg-white/10 transition-colors">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        <span className="text-2xl">{icon}</span>
      </div>
      <div className="space-y-2">
        <p className="text-3xl font-bold text-white">{value}</p>
        <p className="text-gray-300 text-sm">{description}</p>
      </div>
    </div>
  );
}

function ScheduleItem({ time, subject, class: className, room }: {
  time: string;
  subject: string;
  class: string;
  room: string;
}) {
  return (
    <div className="flex items-center space-x-4 p-3 rounded-md hover:bg-white/5 transition-colors">
      <div className="w-16 text-blue-400 font-medium text-sm">{time}</div>
      <div className="flex-1">
        <h4 className="text-white font-medium">{subject}</h4>
        <p className="text-gray-300 text-sm">{className} • {room}</p>
      </div>
      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
    </div>
  );
}