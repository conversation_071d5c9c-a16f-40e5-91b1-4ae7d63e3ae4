//components/home/<USER>
'use client';

interface FeatureSectionProps {
  userType: 'student' | 'teacher' | 'school_admin';
}

const FeatureSection = ({ userType }: FeatureSectionProps) => {
  const studentFeatures = [
    {
      icon: "📝",
      title: "Authentic Writing",
      description: "Focus on your true voice and ideas without AI interference. Write with integrity and confidence."
    },
    {
      icon: "🔍",
      title: "AI-Free Zone",
      description: "Our platform ensures your work is genuinely yours, helping you build real skills and academic honesty."
    },
    {
      icon: "🛡️",
      title: "Privacy First",
      description: "Your writing is your own. We provide a secure, private space for you to think and create."
    },
    {
      icon: "📈",
      title: "Track Your Growth",
      description: "See your writing skills improve over time with tools that focus on substance, not shortcuts."
    },
    {
      icon: "💡",
      title: "Spark Creativity",
      description: "Develop your unique perspective and critical thinking in an environment that values originality."
    },
    {
      icon: "🤝",
      title: "Fair Assessment",
      description: "Submit your work knowing it will be evaluated fairly on its own merits."
    }
  ];

  const teacherFeatures = [
    {
      icon: "🎯",
      title: "Smart Content Creation",
      description: "AI-powered tools help you create engaging lessons, quizzes, and assignments tailored to your teaching style and student needs."
    },
    {
      icon: "👥",
      title: "Collaborative Networks",
      description: "Connect with fellow educators, share resources, and collaborate on curriculum development in a secure environment."
    },
    {
      icon: "📊",
      title: "Student Analytics",
      description: "Track student progress, engagement levels, and learning outcomes with detailed analytics and reporting tools."
    },
    {
      icon: "🎓",
      title: "Professional Development",
      description: "Access training materials, webinars, and certification programs to advance your teaching career."
    },
    {
      icon: "🔒",
      title: "Privacy & Security",
      description: "Your content and student data are protected with enterprise-grade security and privacy controls."
    },
    {
      icon: "📱",
      title: "Mobile Ready",
      description: "Access your teaching materials and student data from any device, anywhere, anytime."
    }
  ];

  const schoolFeatures = [
    {
      icon: "👨‍💼",
      title: "Staff Management",
      description: "Comprehensive dashboard to manage teachers, track performance, and facilitate professional development across your institution."
    },
    {
      icon: "✅",
      title: "Content Approval",
      description: "Streamlined workflows for reviewing and approving educational content to maintain quality standards."
    },
    {
      icon: "📈",
      title: "Institution Analytics",
      description: "Get insights into school-wide performance, resource utilization, and educational outcomes with detailed reporting."
    },
    {
      icon: "🏛️",
      title: "Compliance Management",
      description: "Ensure your institution meets educational standards and regulatory requirements with automated compliance tracking."
    },
    {
      icon: "💰",
      title: "Budget Optimization",
      description: "Track resource allocation, identify cost-saving opportunities, and optimize your educational technology investments."
    },
    {
      icon: "🌐",
      title: "Multi-Campus Support",
      description: "Manage multiple locations, departments, and programs from a single, unified administrative interface."
    }
  ];

  const features = {
    student: studentFeatures,
    teacher: teacherFeatures,
    school_admin: schoolFeatures,
  }[userType];

  const sectionTitle = {
    student: "Write with Confidence and Integrity",
    teacher: "Everything You Need to Excel",
    school_admin: "Powerful Tools for Educational Leaders",
  }[userType];

  const sectionSubtitle = {
    student: "A space designed for pure, undiluted expression and authentic learning.",
    teacher: "Professional-grade tools designed specifically for modern educators.",
    school_admin: "Comprehensive administrative tools for institutions of all sizes.",
  }[userType];

  return (
    <div className="py-20 ">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {sectionTitle}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {sectionSubtitle}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="group p-8 rounded-2xl border border-gray-200 dark:border-zinc-700 hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-lg transition-all duration-300">
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeatureSection;