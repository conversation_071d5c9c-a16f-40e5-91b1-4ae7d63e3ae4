import { LayoutWrapper } from '../components/auth/school/dual-sidebar/layout-wrapper.client';

export default function TestUserNav() {
  return (
    <LayoutWrapper>
      {/* Background content */}
      <div className="p-8">
        <h1 className="text-white text-2xl font-bold mb-4">Integrated UserNav Test (TanStack Style)</h1>
        <p className="text-white/70 mb-4">
          Click the user avatar in the top-right to open the integrated sidebar. 
          The UserNav is now implemented directly in the layout like in your TanStack Start example.
        </p>
        <div className="space-y-4">
          <div className="p-4 bg-white/5 rounded-lg">
            <h2 className="text-white font-semibold mb-2">Expected Behavior:</h2>
            <ul className="text-white/70 space-y-1 text-sm">
              <li>• Click user avatar → Content appears INSTANTLY below the nav buttons</li>
              <li>• Click Settings button → Changes content to settings INSTANTLY</li>
              <li>• Click Notifications button → Changes content to notifications INSTANTLY</li>
              <li>• Active buttons should show visual indicators</li>
              <li>• Tooltips should appear on hover (when not active)</li>
              <li>• Content area is full height and scrollable</li>
              <li>• NO transition delays - everything is instant</li>
              <li>• Unified container with nav buttons at top, content below</li>
              <li>• UserNav implemented directly in layout (like TanStack Start example)</li>
            </ul>
          </div>
          
          <div className="p-4 bg-white/5 rounded-lg">
            <h2 className="text-white font-semibold mb-2">Test Flyouts:</h2>
            <ul className="text-white/70 space-y-1 text-sm">
              <li>• In User Info: Click "Edit Profile" → Opens Account flyout</li>
              <li>• In User Info: Click "Security Settings" → Opens BYOK Setup flyout</li>
              <li>• In Settings: Click "Configure" → Opens Connections flyout</li>
              <li>• In Settings: Click "Billing Settings" → Opens Subscription flyout</li>
            </ul>
          </div>
          
          <div className="p-4 bg-white/5 rounded-lg">
            <h2 className="text-white font-semibold mb-2">Key Differences:</h2>
            <ul className="text-white/70 space-y-1 text-sm">
              <li>• UserNav is now part of the LayoutWrapper (like your TanStack example)</li>
              <li>• No separate UserNav component file needed</li>
              <li>• Content appears directly below navigation buttons</li>
              <li>• All transitions set to duration: 0 for instant response</li>
              <li>• Unified component structure matching your reference implementation</li>
            </ul>
          </div>
        </div>
      </div>
    </LayoutWrapper>
  );
}