import { SquircleProviderSimple } from '../components/providers/squircle-provider-simple.client';
import * as React from 'react';

function BrowserSupportCheck() {
  const [supportInfo, setSupportInfo] = React.useState<{
    hasWorklet: boolean;
    hasHoudini: boolean;
    userAgent: string;
    readyState: string;
  } | null>(null);

  React.useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      setSupportInfo({
        hasWorklet: 'paintWorklet' in CSS,
        hasHoudini: 'CSS' in window && 'paintWorklet' in CSS,
        userAgent: navigator.userAgent,
        readyState: document.readyState
      });
    }
  }, []);

  if (!supportInfo) {
    return (
      <div style={{ marginBottom: '2rem' }}>
        <h2>Browser Support Check</h2>
        <p>Loading browser information...</p>
      </div>
    );
  }

  return (
    <div style={{ marginBottom: '2rem' }}>
      <h2>Browser Support Check</h2>
      <div>
        <p><strong>CSS Paint Worklet Support:</strong> {supportInfo.hasWorklet ? '✅ Yes' : '❌ No'}</p>
        <p><strong>CSS Houdini Support:</strong> {supportInfo.hasHoudini ? '✅ Yes' : '❌ No'}</p>
        <p><strong>User Agent:</strong> {supportInfo.userAgent}</p>
        <p><strong>Document Ready State:</strong> {supportInfo.readyState}</p>
      </div>
    </div>
  );
}

export default function SquircleTestPage() {
  return (
    <SquircleProviderSimple>
      <div style={{ padding: '2rem' }}>
        <h1>Squircle Test Page</h1>
        
        <BrowserSupportCheck />

        <div style={{ marginBottom: '2rem' }}>
          <h2>Squircle Examples</h2>
          
          {/* Basic squircle */}
          <div 
            style={{
              width: '200px',
              height: '100px',
              background: 'paint(squircle)',
              // @ts-ignore - CSS custom properties
              '--squircle-background-color': '#3b82f6',
              '--squircle-border-radius': '20px',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            Basic Squircle
          </div>

          {/* Squircle with border */}
          <div 
            style={{
              width: '200px',
              height: '100px',
              background: 'paint(squircle)',
              // @ts-ignore - CSS custom properties
              '--squircle-background-color': '#10b981',
              '--squircle-border-color': '#059669',
              '--squircle-border-width': '3px',
              '--squircle-border-radius': '25px',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            Bordered Squircle
          </div>

          {/* CSS-only squircle examples */}
          <div 
            className="squircle-css squircle-md squircle-yellow"
            style={{
              width: '200px',
              height: '100px',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            CSS-only Squircle
          </div>

          <div 
            className="squircle-enhanced squircle-lg squircle-purple"
            style={{
              width: '200px',
              height: '100px',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            Enhanced CSS Squircle
          </div>

          <div 
            className="squircle-svg squircle-xl squircle-red"
            style={{
              width: '200px',
              height: '100px',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            SVG Mask Squircle
          </div>

          {/* Fallback with regular border-radius */}
          <div 
            style={{
              width: '200px',
              height: '100px',
              background: '#f59e0b',
              borderRadius: '20px',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            Fallback (Regular Border)
          </div>
        </div>

        <div>
          <h2>Debug Information</h2>
          <p>Check the browser console for detailed initialization logs.</p>
          <p>The new provider automatically falls back to CSS-based rounded corners when worklet loading fails.</p>
          <p>If you see "AbortError", it typically means:</p>
          <ul>
            <li>The CSS Paint Worklet module couldn't be loaded</li>
            <li>CORS issues preventing worklet access</li>
            <li>Build system configuration problems</li>
            <li>Network connectivity issues</li>
          </ul>
          <p><strong>Solution:</strong> The app now uses CSS fallbacks automatically, so squircle shapes will still work!</p>
        </div>
      </div>
    </SquircleProviderSimple>
  );
}