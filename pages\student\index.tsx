// pages/student/index.tsx
import { useSidebarState } from '../../stores/sidebar-store.client';

export default function StudentDashboard() {
  return (
    <div className="p-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-black dark:text-white">Student Dashboard</h1>
          <p className="text-black/70 dark:text-white/70 mt-2 text-lg">Welcome back! Here's what's happening today.</p>
        </div>
        <TestRightSidebarButton />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardCard
          title="My Courses"
          value="8"
          description="Enrolled courses this semester"
          icon="📚"
        />
        <DashboardCard
          title="Assignments"
          value="5"
          description="Pending assignments"
          icon="📝"
        />
        <DashboardCard
          title="Grade Average"
          value="87.5%"
          description="Current semester average"
          icon="📊"
        />
        <DashboardCard
          title="Attendance"
          value="96.2%"
          description="Your attendance rate"
          icon="✅"
        />
        <DashboardCard
          title="Messages"
          value="3"
          description="Unread messages"
          icon="💬"
        />
        <DashboardCard
          title="Events"
          value="7"
          description="Upcoming school events"
          icon="📅"
        />
      </div>

      <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10">
        <h2 className="text-2xl font-semibold text-black dark:text-white mb-6">Today's Classes</h2>
        <div className="space-y-4">
          <ClassItem
            time="09:00 AM"
            subject="Mathematics"
            teacher="Mrs. Johnson"
            room="Room 201"
            status="upcoming"
          />
          <ClassItem
            time="11:00 AM"
            subject="English Literature"
            teacher="Mr. Smith"
            room="Room 105"
            status="upcoming"
          />
          <ClassItem
            time="02:00 PM"
            subject="Physics"
            teacher="Dr. Brown"
            room="Lab 301"
            status="upcoming"
          />
        </div>
      </div>

      <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10">
        <h2 className="text-2xl font-semibold text-black dark:text-white mb-6">Recent Assignments</h2>
        <div className="space-y-4">
          <AssignmentItem
            title="Math Homework Chapter 5"
            subject="Mathematics"
            dueDate="Tomorrow"
            status="pending"
          />
          <AssignmentItem
            title="Essay: Shakespeare Analysis"
            subject="English Literature"
            dueDate="Friday"
            status="pending"
          />
          <AssignmentItem
            title="Physics Lab Report"
            subject="Physics"
            dueDate="Completed"
            status="completed"
          />
        </div>
      </div>
    </div>
  );
}

function TestRightSidebarButton() {
  const { 
    isRightSidebarOpen, 
    rightSidebarContent, 
    updateRightSidebarContent, 
    toggleRightSidebar 
  } = useSidebarState();

  const handleToggle = () => {
    if (!isRightSidebarOpen) {
      updateRightSidebarContent('notifications');
    }
    toggleRightSidebar();
  };

  return (
    <button
      onClick={handleToggle}
      className="px-6 py-3 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-xl transition-colors font-medium"
    >
      {isRightSidebarOpen ? 'Close' : 'Open'} Notifications
    </button>
  );
}

function DashboardCard({ title, value, description, icon }: {
  title: string;
  value: string;
  description: string;
  icon: string;
}) {
  return (
    <div className="bg-white/50 dark:bg-black/20 rounded-xl p-6 border border-black/10 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-colors">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-black dark:text-white">{title}</h3>
        <span className="text-2xl">{icon}</span>
      </div>
      <div className="space-y-2">
        <p className="text-3xl font-bold text-black dark:text-white">{value}</p>
        <p className="text-black/60 dark:text-white/60 text-sm">{description}</p>
      </div>
    </div>
  );
}

function ClassItem({ time, subject, teacher, room, status }: {
  time: string;
  subject: string;
  teacher: string;
  room: string;
  status: 'upcoming' | 'current' | 'completed';
}) {
  const statusColor = {
    upcoming: 'bg-blue-500',
    current: 'bg-green-500',
    completed: 'bg-gray-400'
  }[status];

  return (
    <div className="flex items-center space-x-4 p-4 rounded-lg hover:bg-white/30 dark:hover:bg-black/20 transition-colors border border-black/5 dark:border-white/5">
      <div className="w-20 text-blue-600 dark:text-blue-400 font-medium text-sm">{time}</div>
      <div className="flex-1">
        <h4 className="text-black dark:text-white font-medium text-lg">{subject}</h4>
        <p className="text-black/60 dark:text-white/60 text-sm">{teacher} • {room}</p>
      </div>
      <div className={`w-3 h-3 ${statusColor} rounded-full`}></div>
    </div>
  );
}

function AssignmentItem({ title, subject, dueDate, status }: {
  title: string;
  subject: string;
  dueDate: string;
  status: 'pending' | 'completed';
}) {
  const statusColor = status === 'completed' ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400';

  return (
    <div className="flex items-start space-x-4 p-4 rounded-lg hover:bg-white/30 dark:hover:bg-black/20 transition-colors border border-black/5 dark:border-white/5">
      <div className={`w-3 h-3 ${status === 'completed' ? 'bg-green-500' : 'bg-orange-500'} rounded-full mt-2 flex-shrink-0`}></div>
      <div className="flex-1">
        <h4 className="text-black dark:text-white font-medium text-lg">{title}</h4>
        <p className="text-black/60 dark:text-white/60 text-sm">{subject}</p>
        <p className={`text-sm mt-1 font-medium ${statusColor}`}>Due: {dueDate}</p>
      </div>
    </div>
  );
}
