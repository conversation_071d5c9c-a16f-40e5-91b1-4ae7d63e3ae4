// triggers/user.ts
import type { Add<PERSON>rigger, SetTrigger, GetTrigger } from 'blade/types';

// Trigger for creating users
export const add: AddTrigger = (query) => {
  // Auto-generate slug if not provided
  if (!query.with.slug && query.with.name) {
    query.with.slug = query.with.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  }
  
  // Set default timestamps
  query.with.createdAt = new Date();
  query.with.updatedAt = new Date();
  
  // Set default role if not provided
  if (!query.with.role) {
    query.with.role = 'student';
  }
  
  return query;
};

// Trigger for updating users
export const set: SetTrigger = (query) => {
  // Update timestamp
  query.to.updatedAt = new Date();
  
  // Update slug if name is being changed
  if (query.to.name && !query.to.slug) {
    query.to.slug = query.to.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  }
  
  return query;
};

// Trigger for getting users (can be used for access control)
export const get: GetTrigger = (query) => {
  // Add any access control logic here if needed
  return query;
};