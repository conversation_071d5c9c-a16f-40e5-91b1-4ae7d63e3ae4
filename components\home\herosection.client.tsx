// components/hero-section-with-waitlist.tsx
'use client';

import { WaitlistSignup } from '../waitlist/waitlist-signup.client';
import { Image } from 'blade/client/components';
import NoiseText from './NoiseText.client';

interface HeroSectionProps {
  userType: 'student' | 'teacher' | 'school_admin';
  apiKey: string;
}

const HeroSection = ({ userType, apiKey }: HeroSectionProps) => {
  const studentContent = {
    title: "Pure Writing",
    subtitle: "Your authentic voice without the AI shortcuts",
    image: "/test.png",
  };
  
  const teacherContent = {
    title: "Instant AI Detection",
    subtitle: "Spot artificial content while nurturing real learning",
    image: "/test.png",
  };
  
  const schoolContent = {
    title: "Academic Trust",
    subtitle: "Institution-wide integrity that actually works",
    image: "/test.png",
  };
  
  const getContent = () => {
    switch (userType) {
      case 'student':
        return studentContent;
      case 'teacher':
        return teacherContent;
      case 'school_admin':
        return schoolContent;
      default:
        return studentContent;
    }
  };
  
  const content = getContent();
  
  return (
    <div className="relative overflow-x-hidden">
      <div className="relative max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="items-center">
          <div className="text-center">
            <NoiseText
              text={content.title}
              className="font-manrope_1 text-4xl md:text-5xl lg:text-6xl font-bold mb-2 leading-tight"
            />

            <p className="font-manrope_1 text-md md:text-xl text-black/70 dark:text-white/70 mb-8 leading-relaxed">
              {content.subtitle}
            </p>

            {/* Only show waitlist for teacher and school_admin */}
            {(userType === 'student' || userType === 'teacher' || userType === 'school_admin') && (
              <div className="relative z-10 mb-8">
                <WaitlistSignup
                  userType={userType}
                  placeholder={`Enter your ${userType === 'teacher' ? 'teacher' : 'school'} email`}
                  apiKey={apiKey}
                />
              </div>
            )}
            
           
          </div>
        </div>
        
        <div className="flex justify-center">
          <div className="relative w-[90vw]">
            <div className="flex items-center justify-center">
                <img
                  src={content.image}
                  alt={content.title}
                  className="w-[90vw] h-auto object-contain"
                  
                />
             
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
