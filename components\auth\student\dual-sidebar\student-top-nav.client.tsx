'use client';
import React, { ComponentProps, useState, useCallback } from "react";
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";
import { Sheet, SheetContent, SheetTitle } from "../../../ui/sheet.client";
import { Link, Image } from 'blade/client/components';
import { Tabs } from '@base-ui-components/react/tabs';
import { useQueryState } from 'blade/client/hooks';
import { useLocation } from 'blade/hooks';
import {
  Menu,
  Home,
  FileText,
  Users,
  Calendar,
  Award,
  MessageSquare,
  BookOpen,
  Settings2,
} from "lucide-react";

interface StudentTopNavProps extends ComponentProps<"div"> {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
}

// Student-specific navigation items
const studentNavItems = [
  // Hamburger menu - opens flyout
  {
    id: "menu",
    icon: Menu,
    label: "Menu",
    tooltip: "Navigation Menu",
    type: "flyout" as const,
    section: 1
  },
  // Navigation items that redirect to pages
  {
    id: "To do",
    icon: Home,
    label: "To do",
    tooltip: "Assignments to do",
    type: "link" as const,
    href: "/student/to-do",
    section: 2
  },
  {
    id: "reminders",
    icon: FileText,
    label: "Reminders",
    tooltip: "A reminder of assignment",
    type: "link" as const,
    href: "/student/assignments",
    section: 2
  },
  {
    id: "courses",
    icon: BookOpen,
    label: "Courses",
    tooltip: "My Courses",
    type: "link" as const,
    href: "/student/courses",
    section: 2
  },
  {
    id: "calendar",
    icon: Calendar,
    label: "Calendar",
    tooltip: "Schedule",
    type: "link" as const,
    href: "/student/calendar",
    section: 2
  },
  {
    id: "grades",
    icon: Award,
    label: "Grades",
    tooltip: "My Grades",
    type: "link" as const,
    href: "/student/grades",
    section: 2
  },
  {
    id: "messages",
    icon: MessageSquare,
    label: "Messages",
    tooltip: "Messages",
    type: "link" as const,
    href: "/student/messages",
    section: 2
  },
];

// Flyout menu items for hamburger menu
const flyoutMenuItems = [
  {
    title: "Quick Actions",
    items: [
      { name: "Submit Assignment", href: "/student/assignments/submit", icon: FileText },
      { name: "Join Class", href: "/student/classes/join", icon: Users },
      { name: "View Schedule", href: "/student/schedule", icon: Calendar },
      { name: "Check Grades", href: "/student/grades", icon: Award },
    ]
  },
  {
    title: "Resources",
    items: [
      { name: "Library", href: "/student/library", icon: BookOpen },
      { name: "Study Groups", href: "/student/study-groups", icon: Users },
      { name: "Help Center", href: "/student/help", icon: MessageSquare },
      { name: "Settings", href: "/student/settings", icon: Settings2 },
    ]
  }
];

function IconButton({ 
  item, 
  isActive, 
  onClick,
  isMobile = false,
  showText = false,
  showIcon = true // New prop to control icon visibility
}: { 
  item: typeof studentNavItems[number];
  isActive: boolean;
  onClick: () => void;
  isMobile?: boolean;
  showText?: boolean;
  showIcon?: boolean; // Add this new prop
}) {
  const [isHovered, setIsHovered] = useState(false);

  const buttonContent = (
    <button
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "relative flex font-manrope_1 items-center justify-center gap-2 rounded-xl transition-all duration-300 ease-out transform-gpu will-change-transform overflow-hidden",
        "z-20 touch-manipulation select-none outline-none",
        // Dynamic sizing based on showText and showIcon
        showText && !showIcon ? "h-10 px-4 min-w-fit" : // Text only
        showText && showIcon ? "h-10 px-3 min-w-10" : // Both text and icon
        "w-8 h-8", // Icon only
        // Focus styles for accessibility
      )}
      style={{
        transform: isActive ? 'scale(1.05)' : isHovered ? 'scale(1.02)' : 'scale(1)',
        WebkitTapHighlightColor: 'transparent',
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none',
        touchAction: 'manipulation',
        cursor: 'pointer',
        pointerEvents: 'auto'
      }}
      type="button"
      aria-pressed={isActive}
      aria-label={item.tooltip}
    >
      {/* Main glassmorphism background */}
      <div 
        className="absolute inset-0 rounded-xl transition-all duration-300 ease-out backdrop-blur-[32px] backdrop-saturate-[180%]"
        style={{
          background: isActive
            ? `linear-gradient(135deg, 
                rgba(0, 0, 0, 0.02) 0%, 
                rgba(0, 0, 0, 0.01) 50%, 
                rgba(0, 0, 0, 0.01) 100%
              ),
              radial-gradient(circle at 30% 30%, 
                rgba(0, 0, 0, 0.05) 0%, 
                transparent 70%
              )`
            : isHovered
            ? `linear-gradient(135deg, 
                rgba(0, 0, 0, 0.02) 0%, 
                rgba(0, 0, 0, 0.01) 50%, 
                rgba(0, 0, 0, 0.01) 100%
              )`
            : 'transparent',
          border: isActive
            ? '1px solid rgba(0, 0, 0, 0.05)'
            : isHovered
            ? '1px solid rgba(0, 0, 0, 0.05)'
            : '1px solid transparent'
        }}
      />
      
      {/* Dark mode glassmorphism overlay */}
      <div 
        className="absolute inset-0 rounded-xl transition-all duration-300 ease-out backdrop-blur-[32px] backdrop-saturate-[180%] dark:block hidden"
        style={{
          background: isActive
            ? `linear-gradient(135deg, 
                rgba(255, 255, 255, 0.02) 0%, 
                rgba(255, 255, 255, 0.01) 50%, 
                rgba(255, 255, 255, 0.01) 100%
              ),
              radial-gradient(circle at 30% 30%, 
                rgba(255, 255, 255, 0.05) 0%, 
                transparent 70%
              )`
            : isHovered
            ? `linear-gradient(135deg, 
                rgba(255, 255, 255, 0.02) 0%, 
                rgba(255, 255, 255, 0.01) 50%, 
                rgba(255, 255, 255, 0.01) 100%
              )`
            : 'transparent',
          border: isActive
            ? '1px solid rgba(255, 255, 255, 0.05)'
            : isHovered
            ? '1px solid rgba(255, 255, 255, 0.05)'
            : '1px solid transparent'
        }}
      />

      {/* Icon - only shown when showIcon is true */}
      {showIcon && (
        <item.icon 
          className={cn(
            "w-4 h-4 relative z-10 font-manrope_1 transition-all duration-300 ease-out flex-shrink-0",
            isActive
              ? "text-black dark:text-white drop-shadow-[0_2px_8px_rgba(0,0,0,0.2)] dark:drop-shadow-[0_2px_8px_rgba(255,255,255,0.2)] scale-110"
              : isHovered
              ? "text-gray-800 dark:text-gray-200 scale-105"
              : "text-gray-600 dark:text-gray-400"
          )} 
        />
      )}

      {/* Text label - only shown when showText is true */}
      {showText && (
        <span 
          className={cn(
            "text-sm font-medium relative z-10 transition-all duration-300 ease-out whitespace-nowrap",
            isActive
              ? "text-black dark:text-white"
              : isHovered
              ? "text-gray-800 dark:text-gray-200"
              : "text-gray-600 dark:text-gray-400"
          )}
        >
          {item.label}
        </span>
      )}
    </button>
  );

  // If it's a link type, wrap in Link component
  if (item.type === 'link' && item.href) {
    return (
      <div className="relative group">
        <Link href={item.href}>
          {buttonContent}
        </Link>
        
        {/* Enhanced active indicator */}
        <div 
          className="absolute -bottom-1 left-1/2  transform -translate-x-1/2 transition-all duration-300 ease-out z-20"
          style={{
            opacity: isActive ? 1 : 0,
            transform: `translateX(-50%) scale(${isActive ? 1 : 0.8})`,
          }}
        >
          <div 
            className="w-1.5 h-1.5 rounded-full bg-black dark:bg-white"
            style={{
              boxShadow: '0 0 12px rgba(0, 0, 0, 0.6), 0 0 24px rgba(0, 0, 0, 0.3)',
              filter: 'blur(0.5px)',
            }}
          />
        </div>
        
        {/* No tooltip for link items - they show text labels instead */}
      </div>
    );
  }

  // For flyout type buttons - Fixed positioning
  return (
    <div className="relative group">
      {buttonContent}
      
      {/* Enhanced active indicator - Fixed positioning */}
      <div 
        className="absolute -bottom-1 left-[18px] transform -translate-x-1/2 transition-all duration-300 ease-out z-20"
        style={{
          opacity: isActive ? 1 : 0,
          transform: `translateX(-50%) scale(${isActive ? 1 : 0.8})`,
        }}
      >
        <div 
          className="w-1.5 h-1.5 rounded-full bg-black dark:bg-white"
          style={{
            boxShadow: '0 0 12px rgba(0, 0, 0, 0.6), 0 0 24px rgba(0, 0, 0, 0.3)',
            filter: 'blur(0.5px)',
          }}
        />
      </div>
      
      {/* Hover tooltip - only show when text is not shown and positioned directly beneath button */}
      {!showText && (
        <div
          className="absolute top-12 left-1/2 transform -translate-x-1/2 font-manrope_1 bg-gradient-to-b dark:from-[#f8f9fa] dark:via-[#f8f9fa] dark:to-[#e9ecef] from-[#212026] via-[#212026] to-[#29282e] px-3 text-sm leading-8 text-white/80 dark:text-black/80 dark:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(226_232_240)_inset,0_0.5px_0_1.5px_#64748b_inset] shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] rounded-lg pointer-events-none transition-all duration-300 ease-out whitespace-nowrap z-50"
          style={{
            opacity: isHovered && !isActive ? 1 : 0,
            transform: `translateX(-50%) translateY(${isHovered && !isActive ? '0' : '-8px'})`,
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          }}
        >
          {item.tooltip}
          <div 
            className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1 w-0 h-0 dark:border-b-[#e9ecef] border-b-[#212026]"
            style={{
              borderLeft: '6px solid transparent',
              borderRight: '6px solid transparent',
              borderBottomWidth: '6px',
              borderBottomStyle: 'solid',
            }}
          />
        </div>
      )}
    </div>
  );
}

// Add a Separator component
function Separator() {
  return (
    <div className="w-px h-6 bg-black/10 dark:bg-white/10 mx-1 md:mx-2" />
  );
}

// Mobile Navigation Horizontal Scroller Component
function MobileNavigationScroller() {
  const location = useLocation();
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  
  // Get navigation items for section 2
  const navigationItems = studentNavItems.filter(item => item.section === 2);

  // Determine current active item based on pathname
  const getCurrentActiveItem = () => {
    const currentPath = location.pathname;
    
    // Find matching navigation item
    const matchingItem = navigationItems.find(item => 
      item.href && currentPath.startsWith(item.href)
    );
    
    return matchingItem?.id || null;
  };

  const [activeItem, setActiveItem] = useState(getCurrentActiveItem());

  // Update active item when location changes
  React.useEffect(() => {
    setActiveItem(getCurrentActiveItem());
  }, [location.pathname]);

  // Scroll to active item when it changes
  React.useEffect(() => {
    if (activeItem && scrollContainerRef.current) {
      const activeButton = scrollContainerRef.current.querySelector(`[data-nav-id="${activeItem}"]`) as HTMLElement;
      if (activeButton) {
        activeButton.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [activeItem]);

  const handleItemClick = (item: typeof navigationItems[number]) => {
    setActiveItem(item.id);
    if (item.href) {
      // Navigate to the selected page
      window.location.href = item.href;
    }
  };

  return (
    <div className="flex h-auto gap-1">
      {/* Container with background and shadow - matches the example */}
      <div className="relative">
        {/* Left blur gradient */}
        <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-[#f2f2f2] to-transparent dark:from-[#0d0d0d] z-10 pointer-events-none rounded-l-lg" />
        
        {/* Right blur gradient */}
        <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-[#f2f2f2] to-transparent dark:from-[#0d0d0d] z-10 pointer-events-none rounded-r-lg" />
        
        {/* Scrollable container with proper styling */}
        <div 
          ref={scrollContainerRef}
          className="container flex h-auto w-[220px] flex-nowrap overflow-x-auto rounded-lg px-1 py-1  dark:shadow-inner-shadow-dark-float [&::-webkit-scrollbar]:hidden gap-1"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
        >
          {navigationItems.map((item) => {
            const isActive = activeItem === item.id;
            
            return (
              <button
                key={item.id}
                data-nav-id={item.id}
                onClick={() => handleItemClick(item)}
                aria-label={`Go to ${item.label}`}
                className={cn(
                  "relative flex font-manrope_1 h-8 w-auto min-w-fit mx-3 items-center justify-center whitespace-nowrap rounded-[10px] px-3 text-sm font-medium transition-colors ease-out",
                  isActive 
                    ? "text-black dark:text-white" 
                    : "text-black/40 hover:text-white/40 "
                )}
              >
                {item.label}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
}

export function StudentTopNav({
  flyout,
  setFlyout,
  className,
  ...props
}: StudentTopNavProps) {
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useQueryState('tab');

  // Simplified toggle handler without debouncing - matches working right-sidebar pattern
  const handleToggleFlyout = useCallback((itemId: string) => {
    console.log(`🎯 Toggle flyout for ${itemId}:`, { currentFlyout: flyout, itemId });
    const newFlyout = flyout === itemId ? null : itemId;
    console.log(`🎯 Setting flyout to:`, newFlyout);
    setFlyout(newFlyout);
  }, [flyout, setFlyout]);

  // Handle link navigation (no flyout toggle)
  const handleLinkClick = useCallback((itemId: string) => {
    console.log(`🔗 Link click for ${itemId} - no flyout action needed`);
    // Link navigation is handled by the Link component
  }, []);

  return (
    <div className="flex h-full relative">
      {/* Fixed Logo - Always stays in top-left */}
      <div className="fixed top-4 left-4 z-60 flex items-center">
        <Image
          src="/logo-lightmode.png"
          alt="Penned Logo"
          className="w-8 h-8 dark:hidden"
          width={32}
          height={32}
        />
        <Image
          src="/logo-darkmode.png"
          alt="Penned Logo"
          className="w-8 h-8 hidden dark:block"
          width={32}
          height={32}
        />
      </div>

      {/* Horizontal Navigation Bar */}
      <div className="flex items-center justify-between w-full px-4 h-16 bg-[#f2f2f2] dark:bg-[#0d0d0d]">
        {/* Left Section - Navigation Items (offset to account for fixed logo) */}
        <div className="flex items-center gap-4 ml-12">
          <div className="flex items-center gap-2">
            {/* Hamburger Menu - Use IconButton for section 1 */}
            {studentNavItems
              .filter(item => item.section === 1)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => item.type === 'flyout' ? handleToggleFlyout(item.id) : handleLinkClick(item.id)}
                  isMobile={isMobile}
                  showText={false} // Always false for menu items
                  showIcon={true}  // Always true for menu items
                />
              ))}
            
            <Separator />
            
            {/* Navigation - Mobile uses horizontal scroller, Desktop uses tabs */}
            {isMobile ? (
              <MobileNavigationScroller />
            ) : (
              <Tabs.Root className="relative flex items-center" value={activeTab || 'dashboard'} onValueChange={setActiveTab}>
                <Tabs.List className="relative z-0 flex gap-1 items-center">
                  {studentNavItems
                    .filter(item => item.section === 2)
                    .map((item) => (
                      <Tabs.Tab
                        key={item.id}
                        className="flex h-8 hover:text-black/90 dark:hover:text-white/90 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/40 dark:text-white/40 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value={item.id}
                      >
                        <Link href={item.href}>
                          <span>{item.label}</span>
                        </Link>
                      </Tabs.Tab>
                    ))}
                  <Tabs.Indicator className="absolute top-1/2 left-0 z-[-1] h-8 w-[var(--active-tab-width)] -translate-y-1/2 translate-x-[var(--active-tab-left)] rounded-full bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-100 ease-in-out" />
                </Tabs.List>
              </Tabs.Root>
            )}
          </div>
        </div>

        {/* Right Section - User Navigation is handled by UserNav component */}
        <div className="flex items-center">
          {/* Space for UserNav component which is positioned absolutely */}
        </div>
      </div>

      {/* Flyout Panel for Hamburger Menu - Full Height with Animated Header */}
      {isMobile ? (
        <Sheet open={flyout === 'menu'} onOpenChange={(open) => !open && setFlyout(null)} modal={false}>
          <SheetContent
            side="left"
            className={cn(
              "mobile-sheet-content",
              "border-r border-black/5 dark:border-white/5 p-0 bg-[#f2f2f2] dark:bg-[#0d0d0d]",
              "fixed left-0 top-0 h-screen w-[calc(100vw-0px)] max-w-none",
              "z-[999999]"
            )}
          >
            <SheetTitle className="sr-only">
              Navigation Menu
            </SheetTitle>
            <div className="h-full overflow-hidden">
              {renderFlyoutContent(flyout)}
            </div>
          </SheetContent>
        </Sheet>
      ) : (
        <>
          <div 
            className={cn(
              "fixed left-0 top-0 h-screen w-[1px] bg-[#f2f2f2] dark:bg-[#0d0d0d] z-40",
              "transition-all duration-300 ease-in-out",
              flyout === 'menu' ? "opacity-100" : "opacity-0"
            )}
          />
          <div 
            className={cn(
              "fixed left-0 top-0 h-screen w-[284px] bg-[#f2f2f2] dark:bg-[#0d0d0d] border-r border-black/5 dark:border-white/5 shadow-xl z-40 flex flex-col",
              "transition-all duration-300 ease-in-out",
              flyout === 'menu' ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full pointer-events-none"
            )}
          >
            {/* Simplified content rendering - always render when flyout exists */}
            {flyout === 'menu' && renderFlyoutContent(flyout)}
          </div>
        </>
      )}
    </div>
  );
}

function renderFlyoutContent(flyout: string | null) {
  if (flyout !== 'menu') return null;
  
  return (
    <div className="flex flex-col h-full">
      {/* Header Section with animated text */}
      <div className="relative h-16">
        {/* Static border - appears instantly */}
        <div className="absolute bottom-0 left-0 right-0 border-b border-black/20 dark:border-white/20" />
        
        {/* Header content area */}
        <div className="p-4 flex items-center h-full">
          {/* Invisible spacer to match fixed logo position */}
          <div className="w-8 h-8 mr-3" />
          
          {/* Animated text that slides from logo position */}
          <h2 
            className="text-xl font-semibold text-black dark:text-white overflow-hidden"
            style={{
              animation: flyout === 'menu' ? 'slideFromLogo 0s ease-out' : undefined
            }}
          >
            Student Menu
          </h2>
        </div>
      </div>

      {/* Content Section */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="space-y-6">
          {flyoutMenuItems.map((section, sectionIndex) => (
            <div key={sectionIndex} className="space-y-2">
              <h3 className="text-sm font-medium text-black/70 dark:text-white/70 uppercase tracking-wide">
                {section.title}
              </h3>
              <div className="space-y-1">
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon;
                  return (
                    <Link
                      key={itemIndex}
                      href={item.href}
                    >
                      <div className="flex items-center gap-3 p-3 rounded-md hover:bg-sidebar-accent/50 transition-colors group">
                        <IconComponent className="w-4 h-4 text-black/60 dark:text-white/60 group-hover:text-black dark:group-hover:text-white transition-colors" />
                        <span className="text-sm font-medium text-black/80 dark:text-white/80 group-hover:text-black dark:group-hover:text-white transition-colors">
                          {item.name}
                        </span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Add CSS animation for the slide effect from logo position
const styles = `
  @keyframes slideFromLogo {
    0% {
      transform: translateX(0%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}