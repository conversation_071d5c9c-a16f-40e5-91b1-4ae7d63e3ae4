// pages/test-sidebar.tsx - Public test page for dual sidebar
import { LayoutWrapper } from '../components/auth/school/dual-sidebar';
import { useSidebarState } from '../stores/sidebar-store.client';

export default function TestSidebarPage() {
  return (
    <LayoutWrapper 
      showUserNav={true}
      showHeader={true}
    >
      <TestSidebarContent />
    </LayoutWrapper>
  );
}

function TestSidebarContent() {
  const { 
    isRightSidebarOpen, 
    rightSidebarContent, 
    activeFlyout,
    isRightFlyoutOpen,
    updateRightSidebarContent,
    toggleRightSidebar 
  } = useSidebarState();

  const handleOpenUserInfo = () => {
    updateRightSidebarContent('user-info');
    if (!isRightSidebarOpen) toggleRightSidebar();
  };

  const handleOpenSettings = () => {
    updateRightSidebarContent('settings');
    if (!isRightSidebarOpen) toggleRightSidebar();
  };

  const handleOpenNotifications = () => {
    updateRightSidebarContent('notifications');
    if (!isRightSidebarOpen) toggleRightSidebar();
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Enhanced Dual Sidebar Test</h1>
          <p className="text-gray-300 mt-2">
            Test the enhanced dual sidebar functionality with TanStack-like layout in Blade
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleOpenUserInfo}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            User Info
          </button>
          <button
            onClick={handleOpenSettings}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Settings
          </button>
          <button
            onClick={handleOpenNotifications}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            Notifications
          </button>
          {isRightSidebarOpen && (
            <button
              onClick={toggleRightSidebar}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              Close Sidebar
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <TestCard
          title="Left Sidebar (Icon Mode)"
          description="Icon-based navigation with expandable flyouts"
          features={[
            "Icon rail with glassmorphism effects",
            "Hover tooltips with smooth animations",
            "Expandable flyout panels (278px width)",
            "Notifications, Projects, Runs, Discover, New",
            "Mobile responsive with Sheet component",
            "Active state indicators with glow effects"
          ]}
        />
        <TestCard
          title="Right Sidebar & Flyouts"
          description="Content panels with nested flyout support"
          features={[
            "User info, Settings, Notifications panels",
            "Expandable flyouts (350px additional width)",
            "Mobile sheet behavior with backdrop",
            "Persistent state using Blade cookies",
            "Smooth slide animations with Motion",
            "Z-index layering for proper stacking"
          ]}
        />
        <TestCard
          title="Enhanced Layout System"
          description="TanStack-inspired responsive layout"
          features={[
            "Dynamic margin adjustments (ml-[4.5rem] to ml-[356px])",
            "Right sidebar spacing (mr-[350px] to mr-[700px])",
            "UserNav component with quick actions",
            "Squircle provider integration",
            "Mobile-first responsive design",
            "Blade framework optimized"
          ]}
        />
      </div>

      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Enhanced Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-white mb-3">Left Sidebar Enhancements</h3>
            <ul className="space-y-2 text-gray-300">
              <li>• Glassmorphism background effects with backdrop-blur</li>
              <li>• Enhanced hover states with scale transforms</li>
              <li>• Active indicators with glow and shadow effects</li>
              <li>• Smooth flyout transitions (300ms ease-in-out)</li>
              <li>• Mobile Sheet integration for responsive design</li>
              <li>• Z-index management for proper layering</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-medium text-white mb-3">Right Sidebar & UserNav</h3>
            <ul className="space-y-2 text-gray-300">
              <li>• UserNav component with avatar and quick actions</li>
              <li>• Expandable flyouts with spring animations</li>
              <li>• Tooltip system with proper positioning</li>
              <li>• Cookie-based state persistence</li>
              <li>• Mobile backdrop and touch handling</li>
              <li>• Content-specific panel rendering</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Current State</h2>
        <div className="space-y-3">
          <StateIndicator
            label="Right Sidebar Open"
            value={isRightSidebarOpen ? "Yes" : "No"}
            color={isRightSidebarOpen ? "green" : "red"}
          />
          <StateIndicator
            label="Current Content"
            value={rightSidebarContent || "None"}
            color="blue"
          />
          <StateIndicator
            label="Active Left Flyout"
            value={activeFlyout || "None"}
            color="purple"
          />
          <StateIndicator
            label="Right Flyout Open"
            value={isRightFlyoutOpen ? "Yes" : "No"}
            color="orange"
          />
        </div>
      </div>

      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Technology Integration</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <TechItem name="Blade Framework" description="React-based with SSR" />
          <TechItem name="Squircle Provider" description="Smooth rounded corners" />
          <TechItem name="Motion/React" description="Spring animations" />
          <TechItem name="Tailwind CSS" description="Utility-first styling" />
          <TechItem name="Cookie State" description="Persistent sidebar state" />
          <TechItem name="Sheet Component" description="Mobile responsive panels" />
          <TechItem name="Avatar System" description="User profile display" />
          <TechItem name="Z-Index Management" description="Proper layering system" />
        </div>
      </div>

      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Layout Calculations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-white mb-3">Left Sidebar Margins</h3>
            <ul className="space-y-2 text-gray-300 font-mono text-sm">
              <li>• Icon only: <span className="text-blue-400">ml-[4.5rem]</span> (72px)</li>
              <li>• With flyout: <span className="text-green-400">ml-[356px]</span> (72px + 284px)</li>
              <li>• Mobile: <span className="text-purple-400">ml-[4.5rem]</span> (Sheet overlay)</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-medium text-white mb-3">Right Sidebar Margins</h3>
            <ul className="space-y-2 text-gray-300 font-mono text-sm">
              <li>• Sidebar only: <span className="text-blue-400">mr-[350px]</span></li>
              <li>• With flyout: <span className="text-green-400">mr-[700px]</span> (350px + 350px)</li>
              <li>• Mobile: <span className="text-purple-400">mr-[90vw]</span> or <span className="text-purple-400">sm:mr-[350px]</span></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

function TestCard({ title, description, features }: {
  title: string;
  description: string;
  features: string[];
}) {
  return (
    <div className="bg-white/5 rounded-lg p-6 hover:bg-white/10 transition-colors">
      <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
      <p className="text-gray-300 text-sm mb-4">{description}</p>
      <ul className="space-y-1">
        {features.map((feature, index) => (
          <li key={index} className="text-gray-400 text-xs">• {feature}</li>
        ))}
      </ul>
    </div>
  );
}

function StateIndicator({ label, value, color }: {
  label: string;
  value: string;
  color: string;
}) {
  const colorClasses = {
    green: "text-green-400",
    red: "text-red-400",
    blue: "text-blue-400",
    purple: "text-purple-400"
  };

  return (
    <div className="flex items-center justify-between p-3 bg-white/5 rounded-md">
      <span className="text-gray-300 text-sm">{label}:</span>
      <span className={`text-sm font-medium ${colorClasses[color as keyof typeof colorClasses]}`}>
        {value}
      </span>
    </div>
  );
}

function TechItem({ name, description }: {
  name: string;
  description: string;
}) {
  return (
    <div className="text-center">
      <h4 className="text-white font-medium text-sm">{name}</h4>
      <p className="text-gray-400 text-xs mt-1">{description}</p>
    </div>
  );
}
