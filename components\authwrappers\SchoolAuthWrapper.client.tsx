// components/authwrappers/SchoolAuthWrapper.client.tsx
'use client';

import { useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useRedirect } from 'blade/hooks';

interface SchoolAuthWrapperProps {
  children: React.ReactNode;
}

export const SchoolAuthWrapper: React.FC<SchoolAuthWrapperProps> = ({ children }) => {
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  const redirect = useRedirect();
  
  useEffect(() => {
    // Only redirect when we're sure about auth state
    if (loading) return;
    
    // Redirect if not authenticated
    if (!user) {
      redirect('/login?role=school_admin');
      return;
    }
    
    // Redirect if wrong role
    if (user.role !== 'school_admin') {
      const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
      redirect(`/${rolePrefix}/${user.slug}`);
      return;
    }
  }, [user, loading, redirect]);
  
  // Don't render anything while loading or if user is null/wrong role
  if (loading || !user || user.role !== 'school_admin') {
    return null;
  }
  
  return <>{children}</>;
};