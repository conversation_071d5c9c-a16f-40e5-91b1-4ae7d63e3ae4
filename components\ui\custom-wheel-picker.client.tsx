"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, type PanInfo } from 'motion/react';

export interface WheelPickerOption {
  value: string;
  label: string;
}

interface CustomWheelPickerProps {
  options: WheelPickerOption[];
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  children?: React.ReactNode;
  contentHeight?: number;
  contentWidth?: number;
}

const CustomWheelPicker = ({
  options,
  value,
  onValueChange,
  className,
  children,
  contentHeight = 400,
  contentWidth = 600
}: CustomWheelPickerProps) => {
  const selectedIndex = options.findIndex(option => option.value === value);
  const [currentIndex, setCurrentIndex] = useState(selectedIndex >= 0 ? selectedIndex : 0);
  const [dragOffset, setDragOffset] = useState(0);
  const [isFocused, setIsFocused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const itemHeight = 60;
  const visibleItems = 3;
  const containerHeight = visibleItems * itemHeight;
  
  // Update currentIndex when value prop changes
  useEffect(() => {
    const newIndex = options.findIndex(option => option.value === value);
    if (newIndex >= 0 && newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
    }
  }, [value, options, currentIndex]);

  // Add keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      let newIndex;
      let shouldPreventDefault = false;
      
      if (event.key === 'Tab') {
        shouldPreventDefault = true;
        
        if (event.shiftKey) {
          // Shift+Tab: go backwards (up in the wheel)
          newIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
        } else {
          // Tab: go forwards (down in the wheel)
          newIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
        }
      } else if (event.key === 'ArrowUp') {
        shouldPreventDefault = true;
        // Arrow Up: go backwards (up in the wheel)
        newIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
      } else if (event.key === 'ArrowDown') {
        shouldPreventDefault = true;
        // Arrow Down: go forwards (down in the wheel)
        newIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
      }
      
      if (shouldPreventDefault && newIndex !== undefined) {
        event.preventDefault();
        setCurrentIndex(newIndex);
        const selectedOption = options[newIndex];
        if (selectedOption) {
          onValueChange(selectedOption.value);
        }
      }
    };

    // Add event listener to document
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentIndex, options, onValueChange]);
  
  // Calculate the position for each item
  const getItemPosition = (index: number) => {
    const offset = (index - currentIndex) * itemHeight + dragOffset;
    return offset;
  };

  // Calculate opacity and scale based on distance from center
  const getItemStyle = (index: number) => {
    const distance = Math.abs(index - currentIndex);
    const isSelected = index === currentIndex;
    const opacity = isSelected ? 1 : Math.max(0.4, 1 - (distance * 0.4));
    const scale = Math.max(0.85, 1 - (distance * 0.08));
    
    return {
      opacity,
      scale,
      zIndex: visibleItems - distance,
      isSelected
    };
  };

  const handleDragEnd = (event: any, info: PanInfo) => {
    const velocity = info.velocity.y;
    const offset = info.offset.y;
    
    // Calculate how many items to move based on drag distance and velocity
    const itemsToMove = Math.round((offset + velocity * 0.1) / itemHeight);
    const newIndex = Math.max(0, Math.min(options.length - 1, currentIndex - itemsToMove));
    
    setCurrentIndex(newIndex);
    setDragOffset(0);
    const selectedOption = options[newIndex];
    if (selectedOption) {
      onValueChange(selectedOption.value);
    }
  };

  const handleDrag = (event: any, info: PanInfo) => {
    setDragOffset(info.offset.y);
  };

  const handleOptionClick = (index: number) => {
    setCurrentIndex(index);
    const selectedOption = options[index];
    if (selectedOption) {
      onValueChange(selectedOption.value);
    }
  };

  return (
    <div className={`relative ${className}`}>
     
      
      {/* Selection indicator */}
      <div
        className="absolute left-0 right-0 bg-blue-100/30 dark:bg-blue-800/30 rounded-xl border-2 border-blue-300/50 dark:border-blue-600/50"
        style={{
          height: itemHeight,
          top: '50%',
          transform: 'translateY(-50%)',
          marginLeft: '1rem',
          marginRight: '1rem',
          zIndex: 10
        } as React.CSSProperties}
      />

      {/* Gradient overlays for fade effect */}

      <div
        ref={containerRef}
        tabIndex={0}
        className={`relative overflow-hidden backdrop-blur-md rounded-xl border shadow-lg outline-none transition-all duration-200 ${
          isFocused
            ? 'border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20'
            : 'border-gray-200 dark:border-gray-700'
        }`}
        style={{
          height: containerHeight,
          width: '100%',
          maxWidth: '100%',
          margin: '0 auto'
        } as React.CSSProperties}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
      >
        <motion.div
          className="relative"
          drag="y"
          dragConstraints={{ top: 0, bottom: 0 }}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          dragElastic={0.1}
          dragMomentum={false}
          style={{
            y: 0,
            cursor: 'grab'
          } as any}
          whileDrag={{ cursor: 'grabbing' }}
        >
          {options.map((option, index) => {
            const position = getItemPosition(index);
            const style = getItemStyle(index);
            
            // Only render visible items for performance
            if (Math.abs(position) > containerHeight) return null;
            
            return (
              <motion.div
                key={option.value}
                className={`absolute left-0 right-0 flex items-center justify-center font-manrope_1 font-medium text-lg select-none cursor-pointer transition-colors duration-200 ${
                  style.isSelected
                    ? 'text-gray-900 dark:text-white'
                    : 'text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400'
                }`}
                style={{
                  height: itemHeight,
                  y: position + containerHeight / 2 - itemHeight / 2,
                  opacity: style.opacity,
                  scale: style.scale,
                  zIndex: style.zIndex
                } as any}
                animate={{
                  y: position + containerHeight / 2 - itemHeight / 2,
                  opacity: style.opacity,
                  scale: style.scale
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30
                }}
                onClick={() => handleOptionClick(index)}
                whileHover={{ scale: style.isSelected ? style.scale : style.scale * 1.02 }}
                whileTap={{ scale: style.scale * 0.98 }}
              >
                <div className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  style.isSelected
                    ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-900 dark:text-blue-100'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}>
                  {option.label}
                </div>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
     
    </div>
  );
};

export default CustomWheelPicker;